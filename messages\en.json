{"Common": {"back": "Go Back"}, "NotFound": {"title": "Page not found", "description": "The page you are looking for doesn't exist or has been moved.", "backToHome": "Back to home"}, "Error": {"title": "Something went wrong!", "description": "An unexpected error occurred. Please try again or return to the home page.", "tryAgain": "Try again", "backToHome": "Back to home"}, "Metadata": {"title": "Kurguen Store", "description": "Welcome to Kurguen Store!"}, "Footer": {"backToTop": "back to top", "contactTitle": "general contact", "newsletterTitle": "news and promotions", "newsletterPlaceholder": "add email", "subscribeButton": "subscribe", "copyright": "copyright © 2025 kurguen ltd. luanda-angola. all rights reserved."}, "ProductDetails": {"productName": "kurguen", "productId": "KUR.GA.RK-LUANDA-YELLOW", "price": "295000", "sizePlaceholder": "choose size", "buyNow": "buy now", "addToCart": "add to cart", "detailsTitle": "product details", "careTitle": "care instructions", "sizeLabelPrefix": "Size", "detailsList": {"item1": "polyester and elastane", "item2": "ultra-articulated molded sole", "item3": "Optional completion and lifting insole", "item4": "optional different colored laces included", "item5": "extra comfortable and lightweight", "item6": "memory mold: adapts to the shape of the feet", "item7": "contrasting logo printed on the exterior", "item8": "debossed logo on the metal sock tongue", "item9": "created in UK, designed in Angola, manufactured in Portugal", "item10": "model: kur ga.rk | series: kurglad/25 | color: yellow.brown"}, "careList": {"item1": "clean with a soft, dry, neutral-colored cloth.", "item2": "in contact with liquids, clean immediately and let it air dry.", "item3": "do not use neutral or aggressive chemical products for cleaning.", "item4": "do not machine wash; if possible, consult a cleaning specialist.", "item5": "do not expose this product to direct heat sources or sunlight for prolonged periods.", "item6": "after each use, we advise storing it in the box to protect it from light, dust, and moisture."}, "euSize": "eu size", "ukSize": "uk size", "stock": "stock", "stockStatus": "{count, plural, =0 {sold out} =1 {# left} =2 {# left} =3 {# left} =4 {# left} =5 {# left} =6 {# left} =7 {# left} =8 {# left} =9 {# left} =10 {# left} other {available}}", "selectSizeFirst": "Please select a size first", "addedToCart": "Added to cart successfully"}, "AboutSection": {"title": "kurguen", "subtitle": "it's an African brand with a London flavour topped with a Lisbon touch.", "paragraph1": "it means: \"to love and protect\", and the purest form of protection is that of the family in all its forms, hence the logo representing a family and a shield.", "paragraph2": "kurguen is the next important step, it's your step, at your pace, with your fears, but with the certainty of affirmation!", "paragraph3": "we are cosmopolitan explorers in everything we do: yoga at 6am, capoeira at 11am, padel at 5pm, and kizomba at 11pm...", "paragraph4": "kurguen is about unleashing and promoting excellence. it is a bold movement to elevate African creatives in all sectors, empowering them to achieve and thrive at the highest level of their potential."}, "Header": {"navTop": {"findStore": "Find a Store", "language": "English"}, "navBottom": {"home": "Home", "about": "About Us", "shop": "shop", "contact": "Contact"}}, "HeroSection": {"slide1Title": "OS luanda", "slide1Subtitle": "when quiet luxury hits loud", "date": "Thursday 3 July 2025", "orderButton": "order", "signatureText": "your next big step", "slide2Title": "your next big step", "slide3Title": "edição limitada", "slide4Title": "uma marca africana", "slide3Subtitle": "meet the: kur.ga.rk-luanda-yellow", "slide4Subtitle": "with a london touch, apimentado com um toque lisboeta"}, "CheckoutForm": {"paymentMethodLabel": "Select method of payment", "selectPaymentPlaceholder": "Choose your option", "paymentMethods": {"BANK_TRANSFER": "Bank Transfer", "MULTICAIXA_EXPRESS": "Multicaixa Express", "PAY_IN_STORE": "Pay in Store"}, "BANK_TRANSFER": {"ibanLabel": "IBAN", "entityLabel": "Entity", "nifLabel": "NIF", "bankNameLabel": "Bank", "accountNumberLabel": "Account", "confirmationNote": "Thank you for your preference. You will receive an email or confirmation message. After confirmation, please make the payment to the indicated account within 3 hours.", "paymentDeadlineNote": "The sneakers pickup will be done at the selected store after payment confirmation"}, "MULTICAIXA_EXPRESS": {"numberLabel": "Number", "confirmationNote": "Please add the Order Reference in the Express message.", "paymentDeadlineNote": "Your order must be paid within the next 3 hours, after the confirmation message and pick up within a period of 48 hours."}, "PAY_IN_STORE": {"confirmationNote": "Thank you for your preference. You will receive a confirmation by email or message. After that, head to the selected store to make the payment upon pickup.", "paymentDeadlineNote": "The pickup must be done within 48 hours. Please present proof of reservation at the counter. Kurguen Sales."}, "orderReferenceLabel": "Order Reference", "requiredFieldsLabel": "Required fields", "firstNameLabel": "First Name", "firstNamePlaceholder": "Enter your first name", "lastNameLabel": "Last Name", "lastNamePlaceholder": "Enter your last name", "phoneLabel": "Phone", "phonePlaceholder": "Ex: +244900000000", "emailLabel": "Email", "emailPlaceholder": "Enter your email address", "payNowButton": "Pay Now", "selectStoreLabel": "Store", "selectStoreFirst": "Please select a store before completing your order.", "storeInformation": "Store Information", "storeName": "Name", "storeAddress": "Address", "storePhone": "Phone", "errors": {"selectStoreFirst": "Please select a store first.", "submitOrderError": "Error submitting order", "unexpectedError": "Unexpected error. Please try again."}}, "Auth": {"welcomeBack": "Welcome back", "createAccount": "Create an account", "signInDesc": "Sign in to your account", "updateProfileDesc": "Update your profile information", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "email": "Email", "emailPlaceholder": "<EMAIL>", "signIn": "Sign In", "verificationTitle": "Verification Code", "verificationDesc": "We've sent a code to your email. Please enter it below.", "back": "Back", "verify": "Verify", "noAccount": "Don't have an account?", "signUpLink": "Sign up", "hasAccount": "Already have an account?", "signInLink": "Sign in", "termsText": "By clicking continue, you agree to our", "termsLink": "Terms of Service", "andText": "and", "privacyLink": "Privacy Policy", "updateProfile": "Update Profile", "profileUpdateSuccess": "Profile updated successfully!", "profileUpdateFailed": "Profile update failed. Please try again.", "otpSentSuccess": "OTP sent successfully! Check your email.", "unexpectedError": "An unexpected error occurred. Please try again.", "signOut": "Sign Out", "formDataMissing": "Form data is missing. Please go back and try again.", "otpVerificationFailed": "OTP verification failed.", "authenticationSuccessful": "Authentication successful!"}, "User": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "yourProfile": "Your Profile", "editProfile": "Edit Profile", "logout": "Logout", "signingOut": "Signing out...", "emailInvalid": "Invalid email", "roleInvalid": "Invalid role"}, "Orders": {"yourOrders": "Your Orders", "noOrders": "No orders found", "noOrdersDescription": "You haven't placed any orders yet. When you do, they'll appear here.", "orderNumber": "Order", "size": "Size", "quantity": "Quantity", "price": "Price", "total": "Total", "pickupLocation": "Pickup location", "openMenu": "Open menu", "viewDetails": "View Details", "orderDetails": "Order Details", "customerInfo": "Customer Information", "name": "Name", "email": "Email", "phone": "Phone", "orderItems": "Order Items", "storeInfo": "Store Information", "paymentInfo": "Payment Information", "paymentInstructions": "Payment Instructions", "status": {"PENDING": "Pending", "COMPLETED": "Completed", "CANCELLED": "Cancelled"}}, "CMS": {"dashboard": {"title": "CMS Dashboard - Order Management", "subtitle": "Manage all Kurguen store orders", "loading": "Loading orders...", "stats": {"total": "Total Orders", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled"}, "allOrders": "All Orders", "noOrders": "No orders found", "noOrdersDescription": "There are no orders in the database yet."}, "actions": {"changeStatus": "Change status", "markAs": "<PERSON> as", "confirmTitle": "Change status to {status}", "confirmDescription": "Are you sure you want to change the status of order {orderId} to {status}? This action will send a notification email to the customer.", "cancel": "Cancel", "confirm": "Confirm", "updating": "Updating...", "updateSuccess": "Order status updated successfully!", "updateError": "Error updating order status"}, "sidebar": {"title": "Kurguen CMS", "dashboard": "Dashboard", "users": "Users"}, "users": {"title": "Manage Users", "subtitle": "View and manage all registered users.", "name": "Name", "email": "Email", "phone": "Phone", "role": "Role", "status": "Status", "createdAt": "Created at", "actions": "Actions", "notAvailable": "Not available", "noUsers": "No users found.", "noUsersOnPage": "No users on this page.", "active": "Active", "banned": "Banned", "statusBannedUntil": "Banned until {date}", "users": "Users", "noUsersDescription": "There are no users registered yet.", "ban": "Block", "unban": "Unblock", "revokeSessions": "Revoke Sessions", "remove": "Remove", "previous": "Previous", "next": "Next", "pageInfo": "Page {currentPage} of {totalPages}", "banSuccess": "User banned successfully.", "banError": "Failed to ban user: {error}", "unbanSuccess": "User unbanned successfully.", "unbanError": "Failed to unban user: {error}", "revokeSessionsSuccess": "User sessions revoked successfully.", "revokeSessionsError": "Failed to revoke user sessions: {error}", "removeUserSuccess": "User removed successfully.", "removeUserError": "Failed to remove user: {error}", "genericError": "An unexpected error occurred.", "goToFirstPage": "Go to first page", "loading": "Loading users..."}}, "ShopsSection": {"title": "available in stores", "stores": {"store1": {"name": "<PERSON><PERSON><PERSON>", "address": "Torres do Carmo, Rua Lopes de Lima, Luanda", "phone": "941 656 325"}, "store2": {"name": "<PERSON>", "address": "Casa dos Desportistas, Ilha do Cabo", "phone": "942 872 663"}, "store3": {"name": "Visar (<PERSON><PERSON>)", "address": "Shopping Avennida, Morro Bento, Luanda", "phone": "924 036 367"}, "store4": {"name": "Visar (Talatona)", "address": "Belas Shopping, Talatona, Luanda", "phone": "949 442 308"}}}, "Cart": {"title": "Shopping Cart", "empty": "Your cart is empty", "subtotal": "Subtotal", "total": "Total", "checkout": "Go To Checkout", "close": "Close", "changeSize": "Change", "remove": "Remove", "quantity": "Quantity", "shoppingBag": "Shopping Bag", "continueShopping": "Continue Shopping", "newSeason": "New Season", "lastLeft": "{count, plural, one {Last # left} other {Last # left}}", "sizeLabel": "Size", "changeSizeShort": "Change", "quantityLabel": "Quantity", "outOfStock": "Out of stock", "removeItem": "Remove item", "summaryTitle": "Summary", "openCart": "Open shopping cart", "chooseStore": "choose the store closest to you to pick up your product", "selectStorePlaceholder": "Select a store", "sizeUpdated": "Size updated successfully", "sizeAlreadyExists": "This size already exists in your cart"}, "OrderSubmission": {"validationError": "The provided data is not valid.", "serverError": "Internal server error. Please try again.", "pendingOrderError": "You have a pending order. Please wait for it to be processed."}, "OrderSuccess": {"title": "Order Confirmed!", "subtitle": "Your order has been successfully submitted. Order ID:", "confirmationTitle": "Confirmation Message", "closeButton": "Continue Shopping"}}